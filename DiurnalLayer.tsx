import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import * as SunCalc from 'suncalc';

// Interface pour un nuage
interface Cloud {
  id: number;
  x: number; // Position X initiale aléatoire
  y: number;
  size: number;
  speed: number;
  opacity: number;
  type: 'dust' | 'smoke'; // Deux types de nuages selon tes images
  direction: 'left' | 'right'; // Direction du mouvement
}

// Interface pour un oiseau
interface Bird {
  id: number;
  y: number;
  size: number;
  speed: number;
  direction: 'left' | 'right';
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cloudsRef = useRef<Cloud[]>([]);
  const animationsRef = useRef<gsap.core.Timeline[]>([]);

  // État pour la visibilité des éléments diurnes
  const [diurnalOpacity, setDiurnalOpacity] = useState(0);

  // État pour la géolocalisation (même système que AstronomicalLayer)
  const [userLocation, setUserLocation] = useState<{lat: number, lon: number}>({
    lat: 48.8566, // Paris par défaut
    lon: 2.3522
  });
  const [locationReady, setLocationReady] = useState(false);

  // Générer les nuages avec profondeur de champ et plus de variété
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 12; // Plus de nuages pour un ciel plus vivant

    for (let i = 0; i < cloudCount; i++) {
      const types: ('dust' | 'smoke')[] = ['dust', 'smoke'];
      const directions: ('left' | 'right')[] = ['left', 'right'];

      // Position Y aléatoire dans les 50% supérieurs
      const yPosition = Math.random() * 50 + 5; // Entre 5% et 55% du haut

      // PROFONDEUR DE CHAMP : Plus le nuage est haut, plus il est gros
      // Formule : taille inversement proportionnelle à la position Y
      const depthFactor = 1 - (yPosition - 5) / 50; // 1.0 en haut, 0.0 en bas
      const baseSize = 0.4 + (depthFactor * 1.2); // Taille de base entre 0.4x et 1.6x
      const sizeVariation = Math.random() * 0.3 - 0.15; // Variation ±0.15x
      const finalSize = Math.max(0.2, baseSize + sizeVariation); // Minimum 0.2x

      clouds.push({
        id: i,
        x: Math.random() * 130 - 15, // Position X aléatoire étendue (-15% à 115%)
        y: yPosition,
        size: finalSize,
        speed: Math.random() * 0.4 + 0.1, // Vitesse variable plus réaliste
        opacity: 1.0,
        type: types[Math.floor(Math.random() * types.length)],
        direction: directions[Math.floor(Math.random() * directions.length)]
      });
    }

    return clouds;
  };

  // Générer un oiseau (apparition aléatoire) - Plus visible
  const generateBird = (): Bird => {
    return {
      id: Date.now(), // ID unique basé sur le timestamp
      y: Math.random() * 40 + 15, // Dans les 40% supérieurs, plus bas pour être visible
      size: Math.random() * 0.25 + 0.2, // 0.2x à 0.45x (plus gros pour être visible)
      speed: Math.random() * 0.4 + 0.6, // Vitesse modérée pour être perceptible
      direction: Math.random() > 0.5 ? 'right' : 'left'
    };
  };

  // Calculer l'opacité des éléments diurnes selon l'heure
  const calculateDiurnalOpacity = (currentTime: Date): number => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Période de jour : éléments diurnes visibles
    if (currentHour >= sunrise + 0.5 && currentHour <= sunset - 0.5) {
      return 1.0;
    }

    // Transition progressive après le lever du soleil
    if (currentHour >= sunrise && currentHour < sunrise + 0.5) {
      const progress = (currentHour - sunrise) / 0.5;
      return progress;
    }

    // Transition progressive avant le coucher du soleil
    if (currentHour > sunset - 0.5 && currentHour <= sunset) {
      const progress = (currentHour - (sunset - 0.5)) / 0.5;
      return 1.0 - progress;
    }

    // Nuit : pas d'éléments diurnes
    return 0;
  };

  // Créer l'animation bidirectionnelle d'un nuage avec évaporation
  const createCloudAnimation = (cloudElement: HTMLElement, cloud: Cloud) => {
    const timeline = gsap.timeline({
      repeat: -1,
      force3D: true,
      willChange: "transform"
    });

    // Animation de déplacement selon la direction
    const endX = cloud.direction === 'right' ? '115vw' : '-15vw';
    const totalDuration = 120 / cloud.speed; // Durée totale plus longue
    const evaporationStart = totalDuration * 0.85; // Évaporation commence à 85% du parcours

    // Phase 1 : Déplacement normal (85% du temps)
    timeline.to(cloudElement, {
      x: endX,
      duration: evaporationStart,
      ease: "none"
    });

    // Phase 2 : Évaporation en fondu (15% du temps restant)
    timeline.to(cloudElement, {
      opacity: 0,
      scale: 1.2, // Légère expansion pendant l'évaporation
      duration: totalDuration - evaporationStart,
      ease: "power2.out"
    }, evaporationStart);

    // Phase 3 : Reset instantané pour le cycle suivant
    timeline.set(cloudElement, {
      opacity: 1,
      scale: 1,
      x: cloud.direction === 'right' ? '-15vw' : '115vw'
    });

    // Léger flottement vertical (très subtil)
    gsap.to(cloudElement, {
      y: '+=12',
      duration: 18 + Math.random() * 12,
      repeat: -1,
      yoyo: true,
      ease: "power1.inOut"
    });

    return timeline;
  };

  // Créer l'animation d'un oiseau plus perceptible
  const createBirdAnimation = (birdElement: HTMLElement, bird: Bird) => {
    const startX = bird.direction === 'right' ? '-15vw' : '115vw';
    const endX = bird.direction === 'right' ? '115vw' : '-15vw';

    // Animation de déplacement plus lente et visible
    const timeline = gsap.timeline({
      force3D: true,
      willChange: "transform",
      onComplete: () => {
        // Fondu de sortie avant suppression
        gsap.to(birdElement, {
          opacity: 0,
          duration: 0.5,
          onComplete: () => birdElement.remove()
        });
      }
    });

    // Position initiale
    timeline.set(birdElement, { x: startX, opacity: 0 });

    // Fondu d'entrée
    timeline.to(birdElement, {
      opacity: 1,
      duration: 0.8,
      ease: "power2.out"
    });

    // Déplacement principal plus lent
    timeline.to(birdElement, {
      x: endX,
      duration: 35 / bird.speed, // Plus lent pour être perceptible
      ease: "none"
    }, 0.2);

    // Vol ondulant plus prononcé
    gsap.to(birdElement, {
      y: '+=15',
      duration: 2 + Math.random() * 0.8,
      repeat: -1,
      yoyo: true,
      ease: "power1.inOut"
    });

    // Légère rotation pour simuler le battement d'ailes
    gsap.to(birdElement, {
      rotation: '+=3',
      duration: 0.3 + Math.random() * 0.2,
      repeat: -1,
      yoyo: true,
      ease: "power1.inOut"
    });

    return timeline;
  };

  // Faire apparaître un oiseau de manière aléatoire
  const spawnRandomBird = () => {
    if (!containerRef.current) {
      console.log('❌ Pas de container pour l\'oiseau');
      return;
    }

    if (diurnalOpacity === 0) {
      console.log('❌ Opacité diurne = 0, pas d\'oiseau');
      return;
    }

    const bird = generateBird();
    const birdElement = document.createElement('div');

    birdElement.className = 'absolute pointer-events-none';
    birdElement.style.top = `${bird.y}%`;
    birdElement.style.opacity = '1.0'; // Opacité fixe pour être sûr qu'il soit visible
    birdElement.style.willChange = 'transform, opacity';
    birdElement.style.transform = 'translateZ(0)';
    birdElement.setAttribute('data-bird', 'true');

    console.log(`🐦 Création oiseau: y=${bird.y}%, taille=${bird.size.toFixed(2)}x, direction=${bird.direction}`);

    // Image de l'oiseau plus visible avec meilleur contraste
    birdElement.innerHTML = `
      <img
        src="/oiseau.gif"
        alt="oiseau"
        style="
          width: auto;
          height: ${bird.size * 200}px;
          transform: ${bird.direction === 'left' ? 'scaleX(-1)' : 'scaleX(1)'};
          filter: drop-shadow(0 3px 8px rgba(0,0,0,0.5)) contrast(1.1) brightness(1.05);
        "
        onload="console.log('✅ Oiseau GIF chargé et visible!')"
        onerror="console.error('❌ Erreur chargement oiseau.gif')"
      />
    `;

    containerRef.current.appendChild(birdElement);

    // Créer l'animation
    const birdAnimation = createBirdAnimation(birdElement, bird);
    animationsRef.current.push(birdAnimation);

    console.log('🐦 Oiseau ajouté au DOM et animation lancée');
  };

  // Mettre à jour l'affichage diurne
  const updateDiurnalDisplay = () => {
    if (!locationReady) return;

    const now = new Date();
    const newDiurnalOpacity = calculateDiurnalOpacity(now);
    
    console.log(`☀️ Éléments diurnes - Opacité: ${newDiurnalOpacity.toFixed(2)}`);
    setDiurnalOpacity(newDiurnalOpacity);
  };

  // Démarrer avec Paris par défaut
  useEffect(() => {
    setLocationReady(true);
  }, []);

  // Mise à jour dès que la géolocalisation est prête
  useEffect(() => {
    if (locationReady) {
      updateDiurnalDisplay();
    }
  }, [locationReady]);

  // Initialiser les éléments diurnes avec tes images
  useEffect(() => {
    if (!containerRef.current) return;

    // Générer seulement les nuages (les oiseaux apparaîtront aléatoirement)
    cloudsRef.current = generateClouds();

    // Créer les éléments DOM pour les nuages avec tes images
    cloudsRef.current.forEach((cloud, index) => {
      const cloudElement = document.createElement('div');
      cloudElement.className = 'absolute pointer-events-none';
      cloudElement.style.left = `${cloud.x}%`; // Position X initiale aléatoire
      cloudElement.style.top = `${cloud.y}%`;
      cloudElement.style.opacity = '0';
      cloudElement.setAttribute('data-cloud', 'true');
      cloudElement.style.willChange = 'transform, opacity';
      cloudElement.style.transform = 'translateZ(0)';

      // Image du nuage avec adaptation jour/nuit et profondeur
      const imageSrc = cloud.type === 'dust' ? '/Cloud_white-dust-64.png' : '/smoke-cloud-93.png';
      cloudElement.innerHTML = `
        <img
          src="${imageSrc}"
          alt="nuage"
          class="cloud-image"
          style="
            width: auto;
            height: ${cloud.size * 90}px;
            opacity: 1.0;
            filter: drop-shadow(0 2px 6px rgba(0,0,0,0.2));
            transition: filter 2s ease-in-out;
          "
          onload="console.log('✅ Nuage chargé:', '${imageSrc}', 'Taille:', '${cloud.size.toFixed(2)}x', 'Y:', '${cloud.y.toFixed(1)}%')"
          onerror="console.error('❌ Erreur nuage:', '${imageSrc}')"
        />
      `;

      containerRef.current?.appendChild(cloudElement);

      // Créer l'animation avec délai aléatoire pour éviter l'effet "petit train"
      setTimeout(() => {
        const cloudAnimation = createCloudAnimation(cloudElement, cloud);
        animationsRef.current.push(cloudAnimation);
      }, index * 6000 + Math.random() * 4000); // Délai variable entre 0 et 10 secondes
    });

    // Système d'apparition des oiseaux (plus fréquent pour les tests)
    const birdSpawnInterval = setInterval(() => {
      // Apparition plus fréquente pour les tests : 80% de chance toutes les 5-8 secondes
      if (Math.random() < 0.8 && diurnalOpacity > 0) {
        console.log('🐦 Tentative d\'apparition d\'oiseau...');
        spawnRandomBird();
      }
    }, 5000 + Math.random() * 3000); // Entre 5 et 8 secondes

    // Faire apparaître un oiseau immédiatement pour les tests
    setTimeout(() => {
      if (diurnalOpacity > 0) {
        console.log('🐦 Oiseau de test immédiat');
        spawnRandomBird();
      }
    }, 2000);

    // Mise à jour initiale
    setTimeout(updateDiurnalDisplay, 100);

    // Mise à jour toutes les secondes
    const interval = setInterval(updateDiurnalDisplay, 1000);

    // Nettoyage
    return () => {
      clearInterval(interval);
      clearInterval(birdSpawnInterval);
      animationsRef.current.forEach(animation => animation.kill());
      animationsRef.current = [];
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, []);

  // Animer l'opacité des éléments diurnes avec adaptation nocturne
  useEffect(() => {
    if (!containerRef.current) return;

    const cloudElements = containerRef.current.querySelectorAll('[data-cloud]');
    const birdElements = containerRef.current.querySelectorAll('[data-bird]');

    // Adapter l'apparence des nuages selon l'heure
    cloudElements.forEach((element) => {
      const cloudImg = element.querySelector('.cloud-image') as HTMLImageElement;
      if (cloudImg) {
        // Mode nocturne : nuages plus sombres
        if (diurnalOpacity < 0.3) {
          cloudImg.style.filter = 'drop-shadow(0 2px 6px rgba(0,0,0,0.4)) brightness(0.6) contrast(0.8)';
        } else {
          cloudImg.style.filter = 'drop-shadow(0 2px 6px rgba(0,0,0,0.2)) brightness(1.0) contrast(1.0)';
        }
      }
    });

    // Animer l'opacité globale
    [...cloudElements, ...birdElements].forEach((element) => {
      gsap.to(element, {
        opacity: diurnalOpacity,
        duration: 2,
        ease: "power2.out"
      });
    });

    // Gérer les animations selon la visibilité
    if (diurnalOpacity === 0) {
      animationsRef.current.forEach(animation => animation.pause());
    } else {
      animationsRef.current.forEach(animation => animation.resume());
    }
  }, [diurnalOpacity]);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 2 }} // Au-dessus des étoiles (z-index 0) mais sous le contenu
    />
  );
};

export default DiurnalLayer;
