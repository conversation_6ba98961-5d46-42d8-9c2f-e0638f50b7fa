import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import * as SunCalc from 'suncalc';

// Interface pour un nuage
interface Cloud {
  id: number;
  x: number; // Position X initiale aléatoire
  y: number;
  size: number;
  speed: number;
  opacity: number;
  type: 'dust' | 'smoke'; // Deux types de nuages selon tes images
  direction: 'left' | 'right'; // Direction du mouvement
}

// Interface pour un oiseau
interface Bird {
  id: number;
  y: number;
  size: number;
  speed: number;
  direction: 'left' | 'right';
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cloudsRef = useRef<Cloud[]>([]);
  const animationsRef = useRef<gsap.core.Timeline[]>([]);

  // État pour la visibilité des éléments diurnes
  const [diurnalOpacity, setDiurnalOpacity] = useState(0);

  // État pour la géolocalisation (même système que AstronomicalLayer)
  const [userLocation, setUserLocation] = useState<{lat: number, lon: number}>({
    lat: 48.8566, // Paris par défaut
    lon: 2.3522
  });
  const [locationReady, setLocationReady] = useState(false);

  // Générer les nuages avec plus de variété et réalisme
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 6; // 6 nuages pour plus de variété

    for (let i = 0; i < cloudCount; i++) {
      const types: ('dust' | 'smoke')[] = ['dust', 'smoke'];
      const directions: ('left' | 'right')[] = ['left', 'right'];

      // Créer 3 catégories de tailles : petit, moyen, gros
      let sizeCategory = i % 3;
      let size: number;

      if (sizeCategory === 0) {
        size = Math.random() * 0.3 + 0.3; // Petits : 0.3x à 0.6x
      } else if (sizeCategory === 1) {
        size = Math.random() * 0.4 + 0.6; // Moyens : 0.6x à 1.0x
      } else {
        size = Math.random() * 0.5 + 1.0; // Gros : 1.0x à 1.5x
      }

      clouds.push({
        id: i,
        x: Math.random() * 120 - 10, // Position X aléatoire sur TOUT l'écran (-10% à 110%)
        y: Math.random() * 40 + 3, // Éparpillés dans les 40% supérieurs
        size: size,
        speed: Math.random() * 0.3 + 0.15, // Vitesse lente pour les nuages
        opacity: 1.0, // OPACITÉ FIXE À 100% - Fini la transparence !
        type: types[Math.floor(Math.random() * types.length)],
        direction: directions[Math.floor(Math.random() * directions.length)]
      });
    }

    return clouds;
  };

  // Générer un oiseau (apparition aléatoire)
  const generateBird = (): Bird => {
    return {
      id: Date.now(), // ID unique basé sur le timestamp
      y: Math.random() * 35 + 10, // Dans les 35% supérieurs
      size: Math.random() * 0.15 + 0.1, // 0.1x à 0.25x (petit, en arrière-plan)
      speed: Math.random() * 0.6 + 0.8, // Plus rapide que les nuages
      direction: Math.random() > 0.5 ? 'right' : 'left'
    };
  };

  // Calculer l'opacité des éléments diurnes selon l'heure
  const calculateDiurnalOpacity = (currentTime: Date): number => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Période de jour : éléments diurnes visibles
    if (currentHour >= sunrise + 0.5 && currentHour <= sunset - 0.5) {
      return 1.0;
    }

    // Transition progressive après le lever du soleil
    if (currentHour >= sunrise && currentHour < sunrise + 0.5) {
      const progress = (currentHour - sunrise) / 0.5;
      return progress;
    }

    // Transition progressive avant le coucher du soleil
    if (currentHour > sunset - 0.5 && currentHour <= sunset) {
      const progress = (currentHour - (sunset - 0.5)) / 0.5;
      return 1.0 - progress;
    }

    // Nuit : pas d'éléments diurnes
    return 0;
  };

  // Créer l'animation bidirectionnelle d'un nuage
  const createCloudAnimation = (cloudElement: HTMLElement, cloud: Cloud) => {
    const timeline = gsap.timeline({
      repeat: -1,
      force3D: true,
      willChange: "transform"
    });

    // Animation de déplacement selon la direction (partir de la position actuelle)
    const endX = cloud.direction === 'right' ? '115vw' : '-15vw';

    timeline.to(cloudElement, {
      x: endX,
      duration: 100 / cloud.speed, // Très lent pour les nuages
      ease: "none"
    });

    // Léger flottement vertical (très subtil)
    gsap.to(cloudElement, {
      y: '+=8',
      duration: 15 + Math.random() * 10,
      repeat: -1,
      yoyo: true,
      ease: "power1.inOut"
    });

    return timeline;
  };

  // Créer l'animation simple d'un oiseau (juste déplacement)
  const createBirdAnimation = (birdElement: HTMLElement, bird: Bird) => {
    const startX = bird.direction === 'right' ? '-10vw' : '110vw';
    const endX = bird.direction === 'right' ? '110vw' : '-10vw';

    // Animation de déplacement simple (pas de repeat, l'oiseau traverse une fois)
    const timeline = gsap.timeline({
      force3D: true,
      willChange: "transform",
      onComplete: () => {
        // Supprimer l'oiseau après passage
        birdElement.remove();
      }
    });

    timeline.fromTo(birdElement,
      { x: startX },
      {
        x: endX,
        duration: 25 / bird.speed,
        ease: "none"
      }
    );

    // Vol ondulant très léger
    gsap.to(birdElement, {
      y: '+=8',
      duration: 1.5 + Math.random(),
      repeat: -1,
      yoyo: true,
      ease: "power1.inOut"
    });

    return timeline;
  };

  // Faire apparaître un oiseau de manière aléatoire
  const spawnRandomBird = () => {
    if (!containerRef.current) {
      console.log('❌ Pas de container pour l\'oiseau');
      return;
    }

    if (diurnalOpacity === 0) {
      console.log('❌ Opacité diurne = 0, pas d\'oiseau');
      return;
    }

    const bird = generateBird();
    const birdElement = document.createElement('div');

    birdElement.className = 'absolute pointer-events-none';
    birdElement.style.top = `${bird.y}%`;
    birdElement.style.opacity = '1.0'; // Opacité fixe pour être sûr qu'il soit visible
    birdElement.style.willChange = 'transform, opacity';
    birdElement.style.transform = 'translateZ(0)';
    birdElement.setAttribute('data-bird', 'true');

    console.log(`🐦 Création oiseau: y=${bird.y}%, taille=${bird.size.toFixed(2)}x, direction=${bird.direction}`);

    // Image de l'oiseau avec tes assets (chemin corrigé)
    birdElement.innerHTML = `
      <img
        src="/oiseau.gif"
        alt="oiseau"
        style="
          width: auto;
          height: ${bird.size * 150}px;
          transform: ${bird.direction === 'left' ? 'scaleX(-1)' : 'scaleX(1)'};
          filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        "
        onload="console.log('✅ Oiseau GIF chargé et visible!')"
        onerror="console.error('❌ Erreur chargement oiseau.gif')"
      />
    `;

    containerRef.current.appendChild(birdElement);

    // Créer l'animation
    const birdAnimation = createBirdAnimation(birdElement, bird);
    animationsRef.current.push(birdAnimation);

    console.log('🐦 Oiseau ajouté au DOM et animation lancée');
  };

  // Mettre à jour l'affichage diurne
  const updateDiurnalDisplay = () => {
    if (!locationReady) return;

    const now = new Date();
    const newDiurnalOpacity = calculateDiurnalOpacity(now);
    
    console.log(`☀️ Éléments diurnes - Opacité: ${newDiurnalOpacity.toFixed(2)}`);
    setDiurnalOpacity(newDiurnalOpacity);
  };

  // Démarrer avec Paris par défaut
  useEffect(() => {
    setLocationReady(true);
  }, []);

  // Mise à jour dès que la géolocalisation est prête
  useEffect(() => {
    if (locationReady) {
      updateDiurnalDisplay();
    }
  }, [locationReady]);

  // Initialiser les éléments diurnes avec tes images
  useEffect(() => {
    if (!containerRef.current) return;

    // Générer seulement les nuages (les oiseaux apparaîtront aléatoirement)
    cloudsRef.current = generateClouds();

    // Créer les éléments DOM pour les nuages avec tes images
    cloudsRef.current.forEach((cloud, index) => {
      const cloudElement = document.createElement('div');
      cloudElement.className = 'absolute pointer-events-none';
      cloudElement.style.left = `${cloud.x}%`; // Position X initiale aléatoire
      cloudElement.style.top = `${cloud.y}%`;
      cloudElement.style.opacity = '0';
      cloudElement.setAttribute('data-cloud', 'true');
      cloudElement.style.willChange = 'transform, opacity';
      cloudElement.style.transform = 'translateZ(0)';

      // Image du nuage avec tes assets (chemins corrigés, opacité fixe)
      const imageSrc = cloud.type === 'dust' ? '/Cloud_white-dust-64.png' : '/smoke-cloud-93.png';
      cloudElement.innerHTML = `
        <img
          src="${imageSrc}"
          alt="nuage"
          style="
            width: auto;
            height: ${cloud.size * 80}px;
            opacity: 1.0;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.15));
          "
          onload="console.log('✅ Nuage chargé:', '${imageSrc}', 'Taille:', '${cloud.size.toFixed(2)}x')"
          onerror="console.error('❌ Erreur nuage:', '${imageSrc}')"
        />
      `;

      containerRef.current?.appendChild(cloudElement);

      // Créer l'animation avec délai pour éviter l'effet "petit train"
      setTimeout(() => {
        const cloudAnimation = createCloudAnimation(cloudElement, cloud);
        animationsRef.current.push(cloudAnimation);
      }, index * 8000); // Délai de 8 secondes entre chaque nuage
    });

    // Système d'apparition des oiseaux (plus fréquent pour les tests)
    const birdSpawnInterval = setInterval(() => {
      // Apparition plus fréquente pour les tests : 80% de chance toutes les 5-8 secondes
      if (Math.random() < 0.8 && diurnalOpacity > 0) {
        console.log('🐦 Tentative d\'apparition d\'oiseau...');
        spawnRandomBird();
      }
    }, 5000 + Math.random() * 3000); // Entre 5 et 8 secondes

    // Faire apparaître un oiseau immédiatement pour les tests
    setTimeout(() => {
      if (diurnalOpacity > 0) {
        console.log('🐦 Oiseau de test immédiat');
        spawnRandomBird();
      }
    }, 2000);

    // Mise à jour initiale
    setTimeout(updateDiurnalDisplay, 100);

    // Mise à jour toutes les secondes
    const interval = setInterval(updateDiurnalDisplay, 1000);

    // Nettoyage
    return () => {
      clearInterval(interval);
      clearInterval(birdSpawnInterval);
      animationsRef.current.forEach(animation => animation.kill());
      animationsRef.current = [];
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, []);

  // Animer l'opacité des éléments diurnes
  useEffect(() => {
    if (!containerRef.current) return;

    const cloudElements = containerRef.current.querySelectorAll('[data-cloud]');
    const birdElements = containerRef.current.querySelectorAll('[data-bird]');

    [...cloudElements, ...birdElements].forEach((element) => {
      gsap.to(element, {
        opacity: diurnalOpacity,
        duration: 2,
        ease: "power2.out"
      });
    });

    // Gérer les animations selon la visibilité
    if (diurnalOpacity === 0) {
      animationsRef.current.forEach(animation => animation.pause());
    } else {
      animationsRef.current.forEach(animation => animation.resume());
    }
  }, [diurnalOpacity]);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 2 }} // Au-dessus des étoiles (z-index 0) mais sous le contenu
    />
  );
};

export default DiurnalLayer;
